/* Today Entries Component Styles */

/* Loading Spinner */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(3px);
}

.spinner-container {
  text-align: center;
}

/* Content Blur */
.content-blur {
  filter: blur(3px);
  pointer-events: none;
}

/* Fade In Animation */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* Dashboard Header */
.dashboard-header {
  margin-bottom: 2rem;
}

.visitor-summary-card {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: white;
  text-align: center;
  padding: 1.5rem 2rem 1rem 2rem;
  border-radius: var(--border-radius-lg);
  box-shadow: 0 10px 20px rgba(67, 97, 238, 0.15);
  transition: all 0.3s ease;
}

.visitor-summary-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(67, 97, 238, 0.2);
}

.visitor-icon {
  width: 60px;
  height: 60px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 0.75rem;
  font-size: 1.75rem;
}

.visitor-count {
  font-size: 2.75rem;
  font-weight: 700;
  margin: 0.5rem 0;
}

.date-display {
  font-size: 1rem;
  opacity: 0.8;
  margin-bottom: 0.25rem;
}

/* Modern Stats Cards */
.modern-stats-card {
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  height: 100%;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.modern-stats-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.modern-stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-right: 1rem;
  background-color: rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
}

.modern-stats-info {
  flex-grow: 1;
}

.modern-stats-value {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.modern-stats-label {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.modern-stats-subtext {
  font-size: 0.875rem;
  opacity: 0.8;
}

/* Background Gradients */
.bg-primary-gradient {
  background: linear-gradient(135deg, #4361ee 0%, #3a0ca3 100%);
  color: white;
}

.bg-success-gradient {
  background: linear-gradient(135deg, #28a745 0%, #208838 100%);
  color: white;
}

.bg-info-gradient {
  background: linear-gradient(135deg, #4cc9f0 0%, #4895ef 100%);
  color: white;
}

/* Chart Container */
.chart-container {
  height: 300px;
  margin-top: 2rem;
  position: relative;
}

/* Member Avatar */
.member-info {
  display: flex;
  align-items: center;
}

.member-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  margin-right: 0.75rem;
  flex-shrink: 0;
}

/* Table Styles */
.modern-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.modern-table th {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.5px;
  padding: 1rem;
  border-bottom: 2px solid var(--border-color);
}

.modern-table td {
  padding: 1rem;
  vertical-align: middle;
  border-bottom: 1px solid var(--border-color);
}

.modern-table tbody tr {
  transition: background-color 0.3s ease;
}

.modern-table tbody tr:hover {
  background-color: var(--primary-light);
}

.modern-table tbody tr.active-entry {
  background-color: rgba(40, 167, 69, 0.05);
}

/* Empty State */
.empty-state {
  padding: 3rem 1rem;
  text-align: center;
  color: var(--text-secondary);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.zoom-in {
  animation: zoomIn 0.5s ease-out;
}

@keyframes zoomIn {
  from { opacity: 0; transform: scale(0.95); }
  to { opacity: 1; transform: scale(1); }
}

/* Dark Mode Support */
[data-theme="dark"] .loading-overlay {
  background-color: rgba(18, 18, 18, 0.8);
}

[data-theme="dark"] .modern-table th {
  background-color: var(--bg-tertiary);
}

[data-theme="dark"] .modern-table tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .modern-table tbody tr.active-entry {
  background-color: rgba(40, 167, 69, 0.1);
}

/* Form controls and labels styling for both light and dark modes */
.form-label, .modern-form-label {
  color: #888888 !important; /* Gray color visible in both modes */
  font-weight: bold;
}

input::placeholder,
textarea::placeholder,
.form-control::placeholder,
.search-input::placeholder {
  color: #888888 !important; /* Gray color visible in both modes */
  opacity: 0.9 !important;
}

/* Specific styling for the search input in today-entries */
.search-container .search-input::placeholder {
  color: #888888 !important; /* Gray color visible in both modes */
  opacity: 0.9 !important;
}

/* Ensure form controls have proper contrast in dark mode */
[data-theme="dark"] .form-control {
  color: var(--text-primary);
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

/* Search Button */
.search-btn {
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 15px;
  font-size: 0.9rem;
}

/* Enhanced Filter Panel Styles */
.filter-panel {
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.filter-panel:hover {
  border-color: var(--primary-light);
  transform: translateY(-2px);
}

.filter-status .modern-badge {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.active-filters-panel {
  animation: slideInDown 0.5s ease-out;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.filter-group {
  background: var(--bg-secondary);
  padding: 1.25rem;
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.filter-group:hover {
  background: var(--bg-tertiary);
  box-shadow: var(--shadow-sm);
}

.filter-group .modern-form-label {
  font-weight: 600;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
}

.search-container {
  position: relative;
}

.member-avatar-small {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 0.75rem;
}

/* Results Summary Cards */
.results-summary {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.summary-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.summary-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.summary-card .card-body {
  padding: 1.5rem;
}

.summary-card i {
  opacity: 0.9;
}

.summary-card h4 {
  font-weight: 700;
  font-size: 2rem;
}

.summary-card small {
  font-weight: 500;
  opacity: 0.9;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Enhanced Table Styles */
.results-table {
  animation: fadeIn 0.8s ease-out;
}

.enhanced-table {
  margin-bottom: 0;
}

.enhanced-table th {
  background: var(--bg-secondary);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.5px;
  padding: 1rem;
  border-bottom: 2px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 10;
}

.enhanced-table th.sortable {
  cursor: pointer;
  transition: all 0.3s ease;
}

.enhanced-table th.sortable:hover {
  background: var(--primary-light);
  color: var(--primary);
}

.sort-icon {
  opacity: 0.5;
  transition: all 0.3s ease;
}

.enhanced-table th.sortable:hover .sort-icon {
  opacity: 1;
  color: var(--primary);
}

.table-row-animated {
  animation: slideInLeft 0.5s ease-out forwards;
  opacity: 0;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.enhanced-table td {
  padding: 1rem;
  vertical-align: middle;
  border-bottom: 1px solid var(--border-color);
}

.enhanced-table tbody tr {
  transition: all 0.3s ease;
}

.enhanced-table tbody tr:hover {
  background: var(--primary-light);
  transform: scale(1.01);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Enhanced Member Info */
.member-info.enhanced {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.member-details {
  flex-grow: 1;
}

.member-name {
  font-weight: 600;
  font-size: 0.95rem;
  margin-bottom: 0.25rem;
}

.member-membership {
  margin-top: 0.25rem;
}

/* Info Styles */
.phone-info, .date-info, .time-info, .duration-info, .status-info {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
}

.entry-time {
  color: var(--success);
  font-weight: 500;
}

.exit-time {
  color: var(--danger);
  font-weight: 500;
}

.duration-info {
  font-weight: 600;
}

/* Enhanced Empty State */
.empty-state.enhanced {
  padding: 3rem 2rem;
  text-align: center;
}

.empty-icon {
  opacity: 0.3;
  margin-bottom: 1rem;
}

.empty-state.enhanced h4 {
  color: var(--text-primary);
  font-weight: 600;
}

.empty-state.enhanced p {
  font-size: 1rem;
  max-width: 400px;
  margin: 0 auto 1.5rem;
}

/* Table Actions */
.table-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.table-actions .btn {
  font-size: 0.875rem;
  padding: 0.375rem 0.75rem;
}

/* Active Entry Highlight */
.active-entry {
  background: linear-gradient(90deg, var(--success-light), transparent) !important;
  border-left: 4px solid var(--success);
}

.active-entry:hover {
  background: linear-gradient(90deg, var(--success-light), var(--primary-light)) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .filter-controls .row {
    gap: 1rem;
  }

  .filter-group {
    padding: 1rem;
  }

  .results-summary .col-md-4 {
    margin-bottom: 1rem;
  }

  .summary-card h4 {
    font-size: 1.5rem;
  }

  .table-actions {
    flex-direction: column;
    gap: 0.25rem;
  }

  .table-actions .btn {
    width: 100%;
    font-size: 0.8rem;
  }

  .enhanced-table th,
  .enhanced-table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.85rem;
  }

  .member-info.enhanced {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .phone-info, .date-info, .time-info, .duration-info, .status-info {
    font-size: 0.8rem;
  }
}

/* Responsive Adjustments */
@media (max-width: 767.98px) {
  .visitor-count {
    font-size: 2.5rem;
  }
  
  .visitor-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }
  
  .modern-stats-card {
    margin-bottom: 1rem;
  }
  
  .modern-stats-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }
  
  .modern-stats-value {
    font-size: 1.5rem;
  }
  
  .chart-container {
    height: 250px;
  }
  
  .table-actions {
    margin-top: 1rem;
    display: flex;
    justify-content: space-between;
    width: 100%;
  }
  
  .table-actions button {
    flex: 1;
  }
}
