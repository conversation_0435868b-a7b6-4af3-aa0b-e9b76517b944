/* Debtor Member Component Styles */

/* Loading Spinner */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(3px);
}

.spinner-container {
  text-align: center;
}

/* Content Blur */
.content-blur {
  filter: blur(3px);
  pointer-events: none;
}

/* Fade In Animation */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* Modern Stats Cards */
.modern-stats-card {
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  height: 100%;
  display: flex;
  align-items: center;
  transition: all var(--transition-speed) var(--transition-timing);
  box-shadow: var(--shadow-sm);
  margin-bottom: 1rem;
  position: relative;
  overflow: hidden;
}

.modern-stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  opacity: 0.9;
  z-index: 1;
}

.modern-stats-card > * {
  position: relative;
  z-index: 2;
}

.modern-stats-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.modern-stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-right: 1rem;
  background-color: rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.modern-stats-info {
  flex-grow: 1;
}

.modern-stats-value {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
  color: var(--white);
}

.modern-stats-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Background Gradients */
.bg-primary-gradient {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: var(--white);
}

.bg-warning-gradient {
  background: linear-gradient(135deg, var(--warning) 0%, #e0a800 100%);
  color: var(--white);
}

.bg-info-gradient {
  background: linear-gradient(135deg, var(--info) 0%, #0097b2 100%);
  color: var(--white);
}

.bg-danger-gradient {
  background: linear-gradient(135deg, var(--danger) 0%, #c82333 100%);
  color: var(--white);
}

/* Member Avatar */
.member-info {
  display: flex;
  align-items: center;
}

.member-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  margin-right: 0.75rem;
  flex-shrink: 0;
}

/* Progress Bar */
.progress-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress {
  flex-grow: 1;
  height: 8px;
  background-color: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  border-radius: 4px;
  transition: width 0.6s ease;
}

.progress-text {
  font-size: 0.875rem;
  font-weight: 600;
  min-width: 40px;
  text-align: right;
}

/* Header Styles */
.header-title h5 {
  color: var(--text-primary);
  font-weight: 600;
}

.header-title p {
  font-size: 0.875rem;
  color: var(--text-muted);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Modern Search Box */
.search-container {
  position: relative;
}

.modern-search-box {
  position: relative;
  display: flex;
  align-items: center;
  width: 300px;
}

.search-icon {
  position: absolute;
  left: 1rem;
  color: var(--text-secondary);
  z-index: 2;
  font-size: 0.875rem;
}

.modern-form-control {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all var(--transition-speed) var(--transition-timing);
}

.modern-form-control:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem var(--primary-light);
}

.modern-form-control::placeholder {
  color: var(--text-secondary);
}

/* Legacy Search Box Support */
.search-box {
  max-width: 300px;
}

/* Dark mode support for search box */
[data-theme="dark"] .search-box .input-group-text {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border-color: var(--border-color);
}

[data-theme="dark"] .search-box .form-control {
  background-color: var(--input-bg);
  color: var(--input-text);
  border-color: var(--border-color);
}

[data-theme="dark"] .search-box .form-control::placeholder {
  color: var(--text-secondary);
}

/* Dark Mode Support for Stats Cards */
[data-theme="dark"] .modern-stats-card {
  box-shadow: var(--shadow-sm);
}

[data-theme="dark"] .modern-stats-card:hover {
  box-shadow: var(--shadow-md);
}

[data-theme="dark"] .modern-stats-icon {
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

/* Modern Search Box Dark Mode */
[data-theme="dark"] .modern-form-control {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border-color: var(--border-color);
}

[data-theme="dark"] .modern-form-control:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

[data-theme="dark"] .modern-form-control::placeholder {
  color: var(--text-secondary);
}

[data-theme="dark"] .search-icon {
  color: var(--text-secondary);
}

/* Enhanced Dark Mode Gradients */
[data-theme="dark"] .bg-primary-gradient {
  background: linear-gradient(135deg, var(--primary) 0%, #3a0ca3 100%);
}

[data-theme="dark"] .bg-danger-gradient {
  background: linear-gradient(135deg, #dc3545 0%, #b02a37 100%);
}

[data-theme="dark"] .bg-warning-gradient {
  background: linear-gradient(135deg, #ffc107 0%, #d39e00 100%);
}

[data-theme="dark"] .bg-info-gradient {
  background: linear-gradient(135deg, #0dcaf0 0%, #0086a3 100%);
}

/* Table Styles */
.modern-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.modern-table th {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.5px;
  padding: 1rem;
  border-bottom: 2px solid var(--border-color);
}

.modern-table td {
  padding: 1rem;
  vertical-align: middle;
  border-bottom: 1px solid var(--border-color);
}

.modern-table tbody tr {
  transition: background-color 0.3s ease;
}

.modern-table tbody tr:hover {
  background-color: var(--primary-light);
}

/* Empty State */
.empty-state {
  padding: 3rem 1rem;
  text-align: center;
  color: var(--text-secondary);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Dark Mode Support */
[data-theme="dark"] .loading-overlay {
  background-color: rgba(18, 18, 18, 0.8);
}

[data-theme="dark"] .modern-table {
  background-color: var(--bg-secondary);
}

[data-theme="dark"] .modern-table th {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

[data-theme="dark"] .modern-table td {
  color: var(--text-primary);
  border-bottom-color: var(--border-color);
}

[data-theme="dark"] .modern-table tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .progress {
  background-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .empty-state {
  color: var(--text-secondary);
}

/* Responsive Adjustments */
@media (max-width: 767.98px) {
  .modern-stats-card {
    margin-bottom: 1rem;
  }
  
  .modern-stats-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }
  
  .modern-stats-value {
    font-size: 1.5rem;
  }
  
  .header-actions {
    margin-top: 1rem;
    width: 100%;
  }

  .search-box {
    max-width: 100%;
    width: 100%;
  }

  .modern-search-box {
    width: 100%;
  }

  .modern-card-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .header-title {
    margin-bottom: 1rem;
  }
  
  .progress-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
  
  .progress-text {
    text-align: left;
  }
}

/* Action Buttons Container */
.action-buttons {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  align-items: center;
}

/* Responsive Action Buttons */
@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
    gap: 0.25rem;
  }

  .action-buttons .modern-btn {
    width: 100%;
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
  }
}
