<div class="container-fluid mt-4" [ngClass]="{'fade-in': !isLoading}">
  <!-- Loading Spinner -->
  <app-loading-spinner
    *ngIf="isLoading"
    [overlay]="true"
    [sidebarAware]="true"
    [showText]="true"
    text="Günlük girişler yükleniyor">
  </app-loading-spinner>

  <!-- Main Content -->
  <div *ngIf="!isLoading" class="main-content">
    <!-- Dashboard Header -->
    <div class="dashboard-header mb-4" *ngIf="!isSearched">
      <div class="modern-card visitor-summary-card">
        <div class="card-body text-center">
          <div class="visitor-icon">
            <i class="fas fa-users"></i>
          </div>
          <h5 class="card-title">Günlük Toplam Ziyaretçi</h5>
          <div class="visitor-count">{{ getTotalVisitorsToday() }}</div>
          <p class="date-display">{{ selectedDate | date : "dd/MM/yyyy" }}</p>
        </div>
      </div>
    </div>

    <!-- Gelişmiş Arama ve Filtreleme Paneli -->
    <div class="modern-card mb-4 filter-panel">
      <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
          <h6 class="mb-0">
            <i class="fas fa-filter me-2"></i>
            Arama ve Filtreleme
          </h6>
          <div class="filter-status">
            <span *ngIf="isSearched" class="modern-badge modern-badge-primary">
              <i class="fas fa-search me-1"></i>
              Filtrelenmiş Sonuçlar
            </span>
            <span *ngIf="selectedDate !== initialDate && !isSearched" class="modern-badge modern-badge-info">
              <i class="fas fa-calendar me-1"></i>
              Tarih Filtresi Aktif
            </span>
          </div>
        </div>
      </div>
      <div class="card-body">
        <!-- Aktif Filtreler Bilgi Paneli -->
        <div *ngIf="isSearched || selectedDate !== initialDate" class="active-filters-panel mb-3">
          <div class="row">
            <div class="col-12">
              <div class="alert alert-info d-flex align-items-center">
                <i class="fas fa-info-circle me-2"></i>
                <div class="flex-grow-1">
                  <strong>Aktif Filtreler:</strong>
                  <span *ngIf="isSearched && memberControl.value" class="ms-2">
                    <i class="fas fa-user me-1"></i>
                    Üye: {{ memberControl.value.name || memberControl.value }}
                  </span>
                  <span *ngIf="selectedDate !== initialDate && !isSearched" class="ms-2">
                    <i class="fas fa-calendar me-1"></i>
                    Tarih: {{ selectedDate | date : "dd/MM/yyyy" }}
                  </span>
                </div>
                <button class="btn btn-sm btn-outline-primary" (click)="clearSearch()">
                  <i class="fas fa-times me-1"></i>
                  Filtreleri Temizle
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Filtreleme Kontrolleri -->
        <div class="filter-controls">
          <div class="row g-3">
            <!-- Üye Arama -->
            <div class="col-lg-6 col-md-12">
              <div class="filter-group">
                <label class="modern-form-label">
                  <i class="fas fa-search me-2 text-primary"></i>
                  Üye Ara
                </label>
                <div class="search-container">
                  <div class="input-group">
                    <span class="input-group-text">
                      <i class="fas fa-user"></i>
                    </span>
                    <input
                      type="text"
                      class="form-control search-input"
                      [matAutocomplete]="auto"
                      [formControl]="memberControl"
                      placeholder="İsim veya telefon numarası ile arama yapın..."
                    />
                    <button
                      *ngIf="shouldShowSearchButton()"
                      class="btn btn-primary"
                      type="button"
                      [disabled]="isSearching"
                      (click)="searchMember()"
                    >
                      <span *ngIf="!isSearching">
                        <i class="fas fa-search me-1"></i>
                        Ara
                      </span>
                      <span *ngIf="isSearching">
                        <i class="fas fa-spinner fa-spin me-1"></i>
                        Aranıyor...
                      </span>
                    </button>
                  </div>
                  <mat-autocomplete
                    #auto="matAutocomplete"
                    [displayWith]="displayMember"
                  >
                    <mat-option
                      *ngFor="let member of filteredMembers | async"
                      [value]="member"
                    >
                      <div class="d-flex align-items-center">
                        <div class="member-avatar-small me-2" [style.backgroundColor]="getAvatarColor(member.name)">
                          {{ getInitials(member.name) }}
                        </div>
                        <div>
                          <div class="fw-medium">{{ member.name }}</div>
                          <small class="text-muted">{{ member.phoneNumber }}</small>
                        </div>
                      </div>
                    </mat-option>
                  </mat-autocomplete>
                </div>
              </div>
            </div>

            <!-- Tarih Seçimi -->
            <div class="col-lg-6 col-md-12" *ngIf="!isSearched">
              <div class="filter-group">
                <label class="modern-form-label">
                  <i class="fas fa-calendar-alt me-2 text-info"></i>
                  Tarih Seçin
                </label>
                <div class="input-group">
                  <span class="input-group-text">
                    <i class="fas fa-calendar"></i>
                  </span>
                  <input
                    type="date"
                    id="dateFilter"
                    class="form-control"
                    [(ngModel)]="selectedDate"
                    (ngModelChange)="onDateChange()"
                  />
                  <button
                    *ngIf="selectedDate !== initialDate"
                    class="btn btn-outline-secondary"
                    type="button"
                    (click)="resetToToday()"
                    title="Bugüne dön"
                  >
                    <i class="fas fa-calendar-day"></i>
                  </button>
                </div>
                <small class="form-text text-muted mt-1">
                  <i class="fas fa-info-circle me-1"></i>
                  Varsayılan: Bugünün tarihi
                </small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Sonuçlar Özeti -->
    <div *ngIf="entries.length > 0" class="results-summary mb-3">
      <div class="row g-3">
        <div class="col-md-4">
          <div class="modern-card summary-card bg-primary-gradient">
            <div class="card-body text-center">
              <i class="fas fa-list-ol fa-2x mb-2"></i>
              <h4 class="mb-1">{{ entries.length }}</h4>
              <small>Toplam Kayıt</small>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="modern-card summary-card bg-success-gradient">
            <div class="card-body text-center">
              <i class="fas fa-running fa-2x mb-2"></i>
              <h4 class="mb-1">{{ getActiveEntriesCount() }}</h4>
              <small>Aktif Üye</small>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="modern-card summary-card bg-info-gradient">
            <div class="card-body text-center">
              <i class="fas fa-clock fa-2x mb-2"></i>
              <h4 class="mb-1">{{ getAverageStayTime() }}</h4>
              <small>Ortalama Süre</small>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Giriş/Çıkış Tablosu -->
    <div class="modern-card results-table">
      <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
          <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            Giriş/Çıkış Kayıtları
            <span *ngIf="isSearched" class="ms-2">
              <span class="modern-badge modern-badge-primary">
                {{ entries.length }} sonuç
              </span>
            </span>
          </h5>
          <div class="table-actions">
            <button class="btn btn-sm btn-outline-primary me-2" (click)="exportToExcel()" *ngIf="entries.length > 0">
              <i class="fas fa-file-excel me-1"></i>
              Excel'e Aktar
            </button>
            <button class="btn btn-sm btn-outline-secondary" (click)="refreshData()">
              <i class="fas fa-sync-alt me-1"></i>
              Yenile
            </button>
          </div>
        </div>
      </div>
      <div class="card-body p-0">
        <div class="table-responsive">
          <table class="modern-table enhanced-table">
            <thead>
              <tr>
                <th class="sortable" (click)="sortBy('name')">
                  <div class="d-flex align-items-center">
                    Üye Adı
                    <i class="fas fa-sort ms-1 sort-icon"></i>
                  </div>
                </th>
                <th class="sortable" (click)="sortBy('phoneNumber')">
                  <div class="d-flex align-items-center">
                    Telefon
                    <i class="fas fa-sort ms-1 sort-icon"></i>
                  </div>
                </th>
                <th class="sortable" (click)="sortBy('entryTime')">
                  <div class="d-flex align-items-center">
                    Giriş Tarihi
                    <i class="fas fa-sort ms-1 sort-icon"></i>
                  </div>
                </th>
                <th class="sortable" (click)="sortBy('entryTime')">
                  <div class="d-flex align-items-center">
                    Giriş Saati
                    <i class="fas fa-sort ms-1 sort-icon"></i>
                  </div>
                </th>
                <th>Çıkış Saati</th>
                <th>Spor Süresi</th>
                <th>Durum</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let entry of entries; let i = index"
                  [ngClass]="{'active-entry': !entry.exitTime, 'table-row-animated': true}"
                  [style.animation-delay]="(i * 50) + 'ms'">
                <td>
                  <div class="member-info enhanced">
                    <div class="member-avatar" [style.backgroundColor]="getAvatarColor(entry.name)">
                      {{ getInitials(entry.name) }}
                    </div>
                    <div class="member-details">
                      <div class="member-name">{{ entry.name }}</div>
                      <div *ngIf="!isSearched" class="member-membership">
                        <span
                          class="modern-badge"
                          [ngClass]="{
                            'modern-badge-danger': entry.remainingDays <= 7,
                            'modern-badge-warning': entry.remainingDays > 7 && entry.remainingDays <= 15,
                            'modern-badge-success': entry.remainingDays > 15
                          }"
                        >
                          <i class="fas fa-calendar-check me-1"></i>
                          {{ entry.remainingDays }} Gün Kaldı
                        </span>
                      </div>
                    </div>
                  </div>
                </td>
                <td>
                  <div class="phone-info">
                    <i class="fas fa-phone me-2 text-muted"></i>
                    {{ entry.phoneNumber }}
                  </div>
                </td>
                <td>
                  <div class="date-info">
                    <i class="fas fa-calendar me-2 text-primary"></i>
                    {{ entry.entryTime | date : "dd/MM/yyyy" }}
                  </div>
                </td>
                <td>
                  <div class="time-info entry-time">
                    <i class="fas fa-sign-in-alt me-2 text-success"></i>
                    {{ entry.entryTime | date : "HH:mm:ss" }}
                  </div>
                </td>
                <td>
                  <div class="time-info exit-time">
                    <span *ngIf="entry.exitTime && !shouldShowQRWarning(entry)">
                      <i class="fas fa-sign-out-alt me-2 text-danger"></i>
                      {{ entry.exitTime | date : "HH:mm:ss" }}
                    </span>
                    <span *ngIf="shouldShowQRWarning(entry)" class="text-warning">
                      <i class="fas fa-exclamation-triangle me-1"></i>
                      Çıkış Yapılmadı
                    </span>
                    <span *ngIf="!entry.exitTime" class="text-muted">
                      <i class="fas fa-minus me-1"></i>
                      Henüz çıkış yapmadı
                    </span>
                  </div>
                </td>
                <td>
                  <div class="duration-info">
                    <span *ngIf="entry.exitTime"
                          [ngClass]="{'text-success': getDurationClass(entry) === 'success',
                                      'text-warning': getDurationClass(entry) === 'warning',
                                      'text-danger': getDurationClass(entry) === 'danger'}">
                      <i class="fas fa-stopwatch me-1"></i>
                      {{ calculateDuration(entry.entryTime, entry.exitTime) }}
                    </span>
                    <span *ngIf="!entry.exitTime" class="text-muted">
                      <i class="fas fa-clock me-1"></i>
                      Devam ediyor...
                    </span>
                  </div>
                </td>
                <td>
                  <div class="status-info">
                    <span *ngIf="!entry.exitTime" class="modern-badge modern-badge-success">
                      <i class="fas fa-running me-1"></i>
                      Aktif
                    </span>
                    <span *ngIf="entry.exitTime" class="modern-badge modern-badge-secondary">
                      <i class="fas fa-check me-1"></i>
                      Tamamlandı
                    </span>
                  </div>
                </td>
              </tr>
              <tr *ngIf="entries.length === 0">
                <td colspan="7" class="text-center py-5">
                  <div class="empty-state enhanced">
                    <div class="empty-icon">
                      <i class="fas fa-search fa-4x"></i>
                    </div>
                    <h4 class="mt-3 mb-2">Kayıt Bulunamadı</h4>
                    <p class="text-muted mb-3">
                      <span *ngIf="isSearched">Arama kriterlerinize uygun kayıt bulunamadı.</span>
                      <span *ngIf="!isSearched">Seçilen tarihte giriş/çıkış kaydı bulunmuyor.</span>
                    </p>
                    <button class="btn btn-outline-primary" (click)="clearSearch()" *ngIf="isSearched || selectedDate !== initialDate">
                      <i class="fas fa-times me-1"></i>
                      Filtreleri Temizle
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

  </div>
</div>
