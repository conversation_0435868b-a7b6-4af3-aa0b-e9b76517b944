import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, FormControl, Validators } from '@angular/forms';
import { MemberService } from '../../services/member.service';
import { TransactionService } from '../../services/transaction.service';
import { ToastrService } from 'ngx-toastr';
import { Member } from '../../models/member';
import { PaginatedResult } from '../../models/pagination';
import { Observable } from 'rxjs';
import { map, startWith } from 'rxjs/operators';
import { MatDialog } from '@angular/material/dialog';
import { UpdateBalanceDialogComponent } from '../update-balance-dialog/update-balance-dialog.component';

@Component({
    selector: 'app-member-balance-topup',
    templateUrl: './member-balance-topup.component.html',
    styleUrls: ['./member-balance-topup.component.css'],
    standalone: false
})
export class MemberBalanceTopupComponent implements OnInit {
  // Form controls
  topupForm: FormGroup;
  memberControl = new FormControl();
  
  // Data properties
  members: Member[] = [];
  filteredMembers: Observable<Member[]>;
  paginatedMembers: PaginatedResult<Member> = {
    data: [],
    pageNumber: 1,
    pageSize: 10,
    totalCount: 0,
    totalPages: 0,
    hasPrevious: false,
    hasNext: false
  };
  isLoading: boolean = false;

  // Search and filter properties
  searchTerm: string = '';
  balanceFilter: string = 'all'; // 'all', 'positive', 'negative'

  // Sorting properties
  sortColumn: string = 'name';
  sortDirection: 'asc' | 'desc' = 'asc';

  // Pagination properties
  currentPage: number = 1;
  pageSize: number = 10;
  pageSizeOptions: number[] = [10, 20, 30];

  constructor(
    private fb: FormBuilder,
    private memberService: MemberService,
    private transactionService: TransactionService,
    private toastrService: ToastrService,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.createTopupForm();
    this.getMembers(); // Bu zaten içinde loadMembersWithBalance() çağırıyor

    this.filteredMembers = this.memberControl.valueChanges.pipe(
      startWith(''),
      map(value => typeof value === 'string' ? value : value?.name),
      map(name => name ? this._filterMembers(name) : this.members.slice())
    );
  }

  createTopupForm() {
    this.topupForm = this.fb.group({
      amount: ['', [Validators.required, Validators.min(0.01)]]
    });
  }

  getMembers() {
    this.isLoading = true;
    this.memberService.getMembers().subscribe(
      response => {
        this.members = response.data;
        this.loadMembersWithBalance();
        this.isLoading = false;
      },
      error => {
        this.toastrService.error('Üye bilgileri yüklenirken bir hata oluştu', 'Hata');
        this.isLoading = false;
      }
    );
  }

  loadMembersWithBalance() {
    this.isLoading = true;
    this.memberService.getMembersWithBalancePaginated(
      this.currentPage,
      this.pageSize,
      this.searchTerm,
      this.balanceFilter
    ).subscribe(
      response => {
        if (response.success) {
          this.paginatedMembers = response.data;
        }
        this.isLoading = false;
      },
      error => {
        this.toastrService.error('Bakiyeli üyeler yüklenirken bir hata oluştu', 'Hata');
        this.isLoading = false;
      }
    );
  }

  refreshData() {
    this.getMembers(); // Bu zaten içinde loadMembersWithBalance() çağırıyor
    this.toastrService.info('Veriler yenilendi', 'Bilgi');
  }

  // Statistics methods - Tüm üyelerden hesaplanır
  getTotalPositiveBalance(): number {
    return this.members
      .filter(member => member.balance > 0)
      .reduce((sum, member) => sum + member.balance, 0);
  }

  getTotalNegativeBalance(): number {
    return this.members
      .filter(member => member.balance < 0)
      .reduce((sum, member) => sum + member.balance, 0);
  }



  getPositiveBalanceCount(): number {
    return this.members.filter(member => member.balance > 0).length;
  }

  getNegativeBalanceCount(): number {
    return this.members.filter(member => member.balance < 0).length;
  }

  getTotalMembersWithBalance(): number {
    return this.members.filter(member => member.balance !== 0).length;
  }

  getFilterText(): string {
    switch (this.balanceFilter) {
      case 'positive':
        return 'Pozitif Bakiye';
      case 'negative':
        return 'Negatif Bakiye';
      default:
        return 'Tümü';
    }
  }

  // Display and filter methods
  displayMember(member: Member): string {
    return member && member.name ? `${member.name} - ${member.phoneNumber}` : '';
  }

  private _filterMembers(name: string): Member[] {
    const filterValue = name.toLowerCase();
    return this.members.filter(member => 
      member.name.toLowerCase().includes(filterValue) || 
      member.phoneNumber.includes(filterValue)
    );
  }

  filterMembers() {
    this.currentPage = 1;
    this.loadMembersWithBalance();
  }

  filterByBalanceType(type: string) {
    this.balanceFilter = type;
    this.currentPage = 1;
    this.loadMembersWithBalance();
  }

  // Pagination methods
  goToPage(page: number): void {
    if (page >= 1 && page <= this.paginatedMembers.totalPages) {
      this.currentPage = page;
      this.loadMembersWithBalance();
    }
  }

  changePageSize(size: number): void {
    this.pageSize = size;
    this.currentPage = 1;
    this.loadMembersWithBalance();
  }

  getPaginationRange(): number[] {
    const range = [];
    const maxPagesToShow = 5;

    let startPage = Math.max(1, this.currentPage - Math.floor(maxPagesToShow / 2));
    let endPage = Math.min(this.paginatedMembers.totalPages, startPage + maxPagesToShow - 1);

    if (endPage - startPage + 1 < maxPagesToShow) {
      startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      range.push(i);
    }

    return range;
  }

  // Sorting methods (backend'de yapılacak, şimdilik basit gösterim)
  getSortIcon(column: string): string {
    return 'fa-sort'; // Backend sıralama yapacağı için şimdilik sabit
  }


  // Transaction methods
  topup() {
    if (this.topupForm.valid && this.memberControl.value) {
      this.isLoading = true;
      const selectedMember = this.memberControl.value;
      const topupData = {
        memberID: selectedMember.memberID,
        amount: this.topupForm.get('amount')?.value,
        transactionType: 'Bakiye Yükleme'
      };

      this.transactionService.addTransaction(topupData).subscribe(
        response => {
          this.toastrService.success('Bakiye yükleme başarılı', 'Başarılı');
          this.topupForm.reset({amount: ''});
          this.memberControl.reset();
          this.getMembers(); // Bu zaten içinde loadMembersWithBalance() çağırıyor
          this.isLoading = false;
        },
        error => {
          this.toastrService.error('Bakiye yükleme başarısız', 'Hata');
          this.isLoading = false;
        }
      );
    }
  }

  openUpdateDialog(member: Member) {
    const dialogRef = this.dialog.open(UpdateBalanceDialogComponent, {
      width: '400px',
      panelClass: 'modern-dialog',
      data: { member: member }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.getMembers(); // Bu zaten içinde loadMembersWithBalance() çağırıyor
      }
    });
  }
}